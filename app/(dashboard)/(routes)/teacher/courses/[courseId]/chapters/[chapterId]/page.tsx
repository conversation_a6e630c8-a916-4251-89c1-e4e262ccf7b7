import { auth } from '@clerk/nextjs/server'
import { redirect } from 'next/navigation'
import Link from 'next/link'
import { ArrowLeft, Eye, LayoutDashboard, Video, CheckCircle, AlertCircle } from 'lucide-react'

import { db } from '@/lib/db'
import { IconBadge } from '@/components/icon-badge'
import { Banner } from '@/components/banner'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'

import { ChapterTitleForm } from './_components/chapter-title-form'
import { ChapterDescriptionForm } from './_components/chapter-description-form'
import { ChapterAccessForm } from './_components/chapter-access-form'
import { ChapterVideoForm } from './_components/chapter-video-form'
import { ChapterActions } from './_components/chapter-actions'

type ChapterIdPageProps = {
  params: Promise<{
    courseId: string
    chapterId: string
  }>
}

const ChapterIdPage = async ({ params }: ChapterIdPageProps) => {
  const resolvedParams = await params
  const { userId } = await auth()

  if (!userId) {
    return redirect('/')
  }

  const chapter = await db.chapter.findUnique({
    where: {
      id: resolvedParams.chapterId,
      courseId: resolvedParams.courseId,
    },
    include: {
      muxData: true,
    },
  })

  if (!chapter) {
    return redirect('/')
  }

  const requiredFields = [chapter.title, chapter.description, chapter.videoUrl]

  const totalFields = requiredFields.length
  const completedFields = requiredFields.filter(Boolean).length

  const completionText = `(${completedFields}/${totalFields})`

  const isComplete = requiredFields.every(Boolean)

  return (
    <>
      {!chapter.isPublished && (
        <Banner variant="warning" label="Bab ini belum diterbitkan. Tidak akan terlihat dalam kursus" />
      )}
      <div className="container mx-auto py-6 px-4 max-w-7xl">
        {/* Header */}
        <div className="mb-8">
          <Link href={`/teacher/courses/${resolvedParams.courseId}`}>
            <Button variant="ghost" className="mb-4">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Kembali ke Pengaturan Kursus
            </Button>
          </Link>

          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="space-y-2">
              <h1 className="text-3xl font-bold">Pengaturan Bab</h1>
              <p className="text-muted-foreground">
                Kelola konten dan pengaturan bab kursus
              </p>
            </div>
            <ChapterActions
              disabled={!isComplete}
              courseId={resolvedParams.courseId}
              chapterId={resolvedParams.chapterId}
              isPublished={chapter.isPublished}
            />
          </div>
        </div>

        {/* Progress Card */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {isComplete ? (
                <CheckCircle className="h-5 w-5 text-primary" />
              ) : (
                <AlertCircle className="h-5 w-5 text-muted-foreground" />
              )}
              Kelengkapan Bab
            </CardTitle>
            <CardDescription>
              Lengkapi semua field yang diperlukan untuk menerbitkan bab
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between text-sm">
                <span>Progress: {completedFields} dari {totalFields} field</span>
                <Badge variant={isComplete ? "default" : "secondary"}>
                  {isComplete ? "Siap Diterbitkan" : "Belum Lengkap"}
                </Badge>
              </div>
              <Progress value={(completedFields / totalFields) * 100} className="h-2" />
            </div>
          </CardContent>
        </Card>
        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column - Chapter Content */}
          <div className="space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <IconBadge icon={LayoutDashboard} size="sm" />
                  Informasi Bab
                </CardTitle>
                <CardDescription>
                  Kustomisasi informasi dasar bab kursus
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <ChapterTitleForm
                  initialData={chapter}
                  courseId={resolvedParams.courseId}
                  chapterId={resolvedParams.chapterId}
                />
                <ChapterDescriptionForm
                  initialData={chapter}
                  courseId={resolvedParams.courseId}
                  chapterId={resolvedParams.chapterId}
                />
              </CardContent>
            </Card>

            {/* Access Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <IconBadge icon={Eye} size="sm" />
                  Pengaturan Akses
                </CardTitle>
                <CardDescription>
                  Tentukan siapa yang dapat mengakses bab ini
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ChapterAccessForm
                  initialData={chapter}
                  courseId={resolvedParams.courseId}
                  chapterId={resolvedParams.chapterId}
                />
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Video Content */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <IconBadge icon={Video} size="sm" />
                  Video Pembelajaran
                </CardTitle>
                <CardDescription>
                  Upload video utama untuk bab ini
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ChapterVideoForm
                  initialData={chapter}
                  chapterId={resolvedParams.chapterId}
                  courseId={resolvedParams.courseId}
                />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </>
  )
}

export default ChapterIdPage
