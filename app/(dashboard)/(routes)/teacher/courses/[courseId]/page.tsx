import { auth } from '@clerk/nextjs/server'
import { redirect } from 'next/navigation'
import { CircleDollarSign, File, LayoutDashboard, ListChecks, ArrowLeft, CheckCircle, AlertCircle } from 'lucide-react'
import Link from 'next/link'

import { db } from '@/lib/db'
import { IconBadge } from '@/components/icon-badge'
import { TitleForm } from './_components/title-form'
import { DescriptionForm } from './_components/description-form'
import { ImageForm } from './_components/image-form'
import CategoryForm from './_components/category-form'
import { PriceForm } from './_components/price-form'
import { AttachmentForm } from './_components/attachment-form'
import { ChaptersForm } from './_components/chapters-form'
import { Banner } from '@/components/banner'
import Actions from './_components/actions'
import { CourseReviewsSection } from './_components/course-reviews-section'
import { CourseCertificatesSection } from './_components/course-certificates-section'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'

type CourseIdPageProps = {
  params: Promise<{
    courseId: string
  }>
}

const CourseIdPage = async ({ params }: CourseIdPageProps) => {
  const resolvedParams = await params
  const { userId } = await auth()

  if (!userId) {
    return redirect('/')
  }

  const course = await db.course.findUnique({
    where: { id: resolvedParams.courseId, createdById: userId },
    include: { attachments: { orderBy: { createdAt: 'desc' } }, chapters: { orderBy: { position: 'asc' } } },
  })

  if (!course) {
    return redirect('/')
  }

  const categories = await db.category.findMany({
    orderBy: {
      name: 'asc',
    },
  })

  const requiredFields = [
    course.title,
    course.description,
    course.imageUrl,
    course.isFree || course.price, // Either free or has a price
    course.categoryId,
    course.chapters.some((chapter) => chapter.isPublished),
  ]

  const totalFields = requiredFields.length
  const completedFields = requiredFields.filter(Boolean).length

  const completionText = `(${completedFields}/${totalFields})`

  const isComplete = requiredFields.every(Boolean)

  return (
    <>
      {!course.isPublished && <Banner label="Kursus ini belum diterbitkan. Tidak akan terlihat oleh siswa." />}
      <div className="container mx-auto py-6 px-4 max-w-7xl">
        {/* Header */}
        <div className="mb-8">
          <Link href="/teacher/courses">
            <Button variant="ghost" className="mb-4">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Kembali ke Kursus
            </Button>
          </Link>

          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="space-y-2">
              <h1 className="text-3xl font-bold">Pengaturan Kursus</h1>
              <p className="text-muted-foreground">
                Kelola dan kustomisasi kursus Anda
              </p>
            </div>
            <Actions disabled={!isComplete} courseId={resolvedParams.courseId} isPublished={course.isPublished} />
          </div>
        </div>

        {/* Progress Card */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {isComplete ? (
                <CheckCircle className="h-5 w-5 text-primary" />
              ) : (
                <AlertCircle className="h-5 w-5 text-muted-foreground" />
              )}
              Kelengkapan Kursus
            </CardTitle>
            <CardDescription>
              Lengkapi semua field yang diperlukan untuk menerbitkan kursus
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between text-sm">
                <span>Progress: {completedFields} dari {totalFields} field</span>
                <Badge variant={isComplete ? "default" : "secondary"}>
                  {isComplete ? "Siap Diterbitkan" : "Belum Lengkap"}
                </Badge>
              </div>
              <Progress value={(completedFields / totalFields) * 100} className="h-2" />
            </div>
          </CardContent>
        </Card>
        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column - Course Customization */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <IconBadge icon={LayoutDashboard} size="sm" />
                  Informasi Kursus
                </CardTitle>
                <CardDescription>
                  Kustomisasi informasi dasar kursus Anda
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <TitleForm initialData={course} courseId={course.id} />
                <DescriptionForm initialData={course} courseId={course.id} />
                <ImageForm initialData={course} courseId={course.id} />
                <CategoryForm
                  initialData={course}
                  courseId={course.id}
                  options={categories.map((category) => ({
                    label: category.name,
                    value: category.id,
                  }))}
                />
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Course Content & Settings */}
          <div className="space-y-6">
            {/* Chapters */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <IconBadge icon={ListChecks} size="sm" />
                  Bab Kursus
                </CardTitle>
                <CardDescription>
                  Kelola konten dan struktur kursus Anda
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ChaptersForm initialData={course} courseId={course.id} />
              </CardContent>
            </Card>

            {/* Pricing */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <IconBadge icon={CircleDollarSign} size="sm" />
                  Harga Kursus
                </CardTitle>
                <CardDescription>
                  Tentukan harga dan model bisnis kursus
                </CardDescription>
              </CardHeader>
              <CardContent>
                <PriceForm initialData={course} courseId={course.id} />
              </CardContent>
            </Card>

            {/* Attachments */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <IconBadge icon={File} size="sm" />
                  Lampiran & Sumber Daya
                </CardTitle>
                <CardDescription>
                  Upload file tambahan untuk siswa
                </CardDescription>
              </CardHeader>
              <CardContent>
                <AttachmentForm initialData={course} courseId={course.id} />
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Analytics Section - Only show for published courses */}
        {course.isPublished && (
          <>
            <Separator className="my-8" />
            <div className="space-y-6">
              <div>
                <h2 className="text-2xl font-bold">Analisis Kursus</h2>
                <p className="text-muted-foreground">
                  Pantau performa dan feedback kursus Anda
                </p>
              </div>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <CourseReviewsSection courseId={course.id} />
                <CourseCertificatesSection courseId={course.id} />
              </div>
            </div>
          </>
        )}
      </div>
    </>
  )
}

export default CourseIdPage
