'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import axios from 'axios'
import { DataTable } from './_component/data-table'

interface Course {
  id: string
  title: string
  price: number | null
  isFree: boolean
  isPublished: boolean
  category?: { name: string } | null
  _count: {
    chapters: number
    purchases: number
  }
  createdAt: string
}

interface PaginationData {
  page: number
  limit: number
  totalCourses: number
  totalPages: number
  hasNextPage: boolean
  hasPreviousPage: boolean
}

interface CoursesResponse {
  courses: Course[]
  pagination: PaginationData
}

export default function Courses() {
  const router = useRouter()
  const [courses, setCourses] = useState<Course[]>([])
  const [pagination, setPagination] = useState<PaginationData>({
    page: 1,
    limit: 10,
    totalCourses: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false
  })
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')

  const fetchCourses = async (
    page: number = 1,
    search: string = '',
    status: string = 'all'
  ) => {
    try {
      setIsLoading(true)
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '10',
        search,
        status,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      })

      const response = await axios.get<CoursesResponse>(`/api/courses?${params}`)
      setCourses(response.data.courses)
      setPagination(response.data.pagination)
    } catch (error) {
      console.error('Error fetching courses:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchCourses()
  }, [])

  const handlePageChange = (page: number) => {
    fetchCourses(page, searchQuery, statusFilter)
  }

  const handleSearchChange = (search: string) => {
    setSearchQuery(search)
    // Debounce search
    const timeoutId = setTimeout(() => {
      fetchCourses(1, search, statusFilter)
    }, 500)

    return () => clearTimeout(timeoutId)
  }

  const handleStatusChange = (status: string) => {
    setStatusFilter(status)
    fetchCourses(1, searchQuery, status)
  }

  const handleSortChange = (sortBy: string, sortOrder: string) => {
    // Implementation for sorting if needed
    fetchCourses(pagination.page, searchQuery, statusFilter)
  }

  return (
    <div className="container mx-auto py-6 px-4">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Kelola Kursus</h1>
        <p className="text-muted-foreground">
          Kelola dan pantau semua kursus yang Anda buat
        </p>
      </div>

      <DataTable
        courses={courses}
        pagination={pagination}
        isLoading={isLoading}
        onPageChange={handlePageChange}
        onSearchChange={handleSearchChange}
        onStatusChange={handleStatusChange}
        onSortChange={handleSortChange}
      />
    </div>
  )
}
