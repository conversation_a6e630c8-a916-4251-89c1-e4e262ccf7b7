'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import {
  Users,
  BookOpen,
  TrendingUp,
  Star,
  DollarSign,
  Award,
  MessageSquare,
  CheckCircle
} from 'lucide-react'
import { formatPrice } from '@/lib/format'
import { EnhancedAnalytics } from '@/actions/get-enhanced-analytics'
import { Line, LineChart, ResponsiveContainer, XAxis, YAxis, Tooltip } from 'recharts'

interface EnhancedAnalyticsDashboardProps {
  data: EnhancedAnalytics
}

export function EnhancedAnalyticsDashboard({ data }: EnhancedAnalyticsDashboardProps) {
  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Pendapatan</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatPrice(data.totalRevenue)}</div>
            <p className="text-xs text-muted-foreground">
              Dari {data.totalSales} penjualan
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Siswa</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalStudents}</div>
            <p className="text-xs text-muted-foreground">
              {data.studentEngagement.totalActiveStudents} aktif
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Kursus</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalCourses}</div>
            <p className="text-xs text-muted-foreground">
              Kursus yang dipublikasi
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rata-rata Progress</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {data.studentEngagement.averageProgressPerStudent.toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">
              Progress siswa
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Revenue Trends */}
      <Card>
        <CardHeader>
          <CardTitle>Tren Pendapatan (6 Bulan Terakhir)</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={data.revenueTrends}>
              <XAxis dataKey="month" />
              <YAxis tickFormatter={(value) => `Rp${value.toLocaleString('id-ID')}`} />
              <Tooltip 
                formatter={(value: number) => [`Rp${value.toLocaleString('id-ID')}`, 'Pendapatan']}
              />
              <Line 
                type="monotone" 
                dataKey="revenue" 
                stroke="#0369a1" 
                strokeWidth={2}
                dot={{ fill: '#0369a1' }}
              />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Course Performance */}
      <Card>
        <CardHeader>
          <CardTitle>Performa Kursus</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data.coursePerformance.slice(0, 5).map((course) => (
              <div key={course.courseId} className="space-y-2">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium truncate">{course.title}</h4>
                  <div className="flex items-center gap-2">
                    {course.averageRating && (
                      <Badge variant="secondary" className="flex items-center gap-1">
                        <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                        {course.averageRating.toFixed(1)}
                      </Badge>
                    )}
                    <Badge variant="outline">
                      {course.enrollments} siswa
                    </Badge>
                  </div>
                </div>
                
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">Pendapatan</p>
                    <p className="font-medium">{formatPrice(course.revenue)}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Tingkat Selesai</p>
                    <p className="font-medium">{course.completionRate.toFixed(1)}%</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Review</p>
                    <p className="font-medium">{course.totalReviews}</p>
                  </div>
                </div>
                
                <Progress value={course.completionRate} className="h-2" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Top Courses */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Top Pendapatan</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.topCourses
                .filter(course => course.type === 'revenue')
                .slice(0, 3)
                .map((course, index) => (
                <div key={course.courseId} className="flex items-center gap-3">
                  <Badge variant={index === 0 ? 'default' : 'secondary'}>
                    #{index + 1}
                  </Badge>
                  <div className="flex-1 min-w-0">
                    <p className="font-medium truncate">{course.title}</p>
                    <p className="text-sm text-muted-foreground">
                      {formatPrice(course.metric)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">Top Pendaftaran</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.topCourses
                .filter(course => course.type === 'enrollments')
                .slice(0, 3)
                .map((course, index) => (
                <div key={course.courseId} className="flex items-center gap-3">
                  <Badge variant={index === 0 ? 'default' : 'secondary'}>
                    #{index + 1}
                  </Badge>
                  <div className="flex-1 min-w-0">
                    <p className="font-medium truncate">{course.title}</p>
                    <p className="text-sm text-muted-foreground">
                      {course.metric} siswa
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">Top Rating</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.topCourses
                .filter(course => course.type === 'rating')
                .slice(0, 3)
                .map((course, index) => (
                <div key={course.courseId} className="flex items-center gap-3">
                  <Badge variant={index === 0 ? 'default' : 'secondary'}>
                    #{index + 1}
                  </Badge>
                  <div className="flex-1 min-w-0">
                    <p className="font-medium truncate">{course.title}</p>
                    <div className="flex items-center gap-1">
                      <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                      <span className="text-sm text-muted-foreground">
                        {course.metric.toFixed(1)}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Student Engagement */}
      <Card>
        <CardHeader>
          <CardTitle>Engagement Siswa</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600">
                {data.studentEngagement.totalActiveStudents}
              </div>
              <p className="text-sm text-muted-foreground">Siswa Aktif</p>
            </div>
            
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">
                {data.studentEngagement.averageProgressPerStudent.toFixed(1)}%
              </div>
              <p className="text-sm text-muted-foreground">Rata-rata Progress</p>
            </div>
            
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600">
                {data.studentEngagement.studentsCompletedCourses}
              </div>
              <p className="text-sm text-muted-foreground">Siswa Lulus</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Aktivitas Terbaru</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data.recentActivity.slice(0, 10).map((activity, index) => (
              <div key={index} className="flex items-start gap-3 p-3 border rounded-lg hover:bg-gray-50 transition-colors">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  activity.type === 'enrollment' ? 'bg-blue-100' :
                  activity.type === 'completion' ? 'bg-green-100' :
                  'bg-yellow-100'
                }`}>
                  {activity.type === 'enrollment' && <Users className="h-4 w-4 text-blue-600" />}
                  {activity.type === 'completion' && <CheckCircle className="h-4 w-4 text-green-600" />}
                  {activity.type === 'review' && <MessageSquare className="h-4 w-4 text-yellow-600" />}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <p className="font-medium truncate">{activity.courseName}</p>
                    {activity.type === 'review' && activity.rating && (
                      <div className="flex items-center gap-1">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <Star
                            key={star}
                            className={`h-3 w-3 ${
                              star <= activity.rating! ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
                            }`}
                          />
                        ))}
                      </div>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground">{activity.details}</p>
                  {activity.type === 'review' && activity.comment && (
                    <p className="text-xs text-gray-600 mt-1 italic line-clamp-2">
                      &ldquo;{activity.comment}&rdquo;
                    </p>
                  )}
                </div>
                <div className="text-xs text-muted-foreground flex-shrink-0">
                  {activity.date.toLocaleDateString('id-ID')}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
