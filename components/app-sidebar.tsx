"use client"

import * as React from "react"
import { useAuth } from "@clerk/nextjs"
import { usePathname } from "next/navigation"
import Link from "next/link"
import {
  BarChart,
  Compass,
  Layout,
  List,
  Tags,
  MessageSquare,
  Award,
  GraduationCap,
} from "lucide-react"

import { NavMain } from "@/components/nav-main"
import { NavSecondary } from "@/components/nav-secondary"
import { NavUser } from "@/components/nav-user"
import { isTeacher } from "@/lib/teacher"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"

// Guest routes for students
const guestRoutes = [
  {
    title: "Dasbor",
    url: "/",
    icon: Layout,
    isActive: false,
  },
  {
    title: "Jelajahi",
    url: "/search",
    icon: Compass,
    isActive: false,
  },
  {
    title: "Serti<PERSON>kat",
    url: "/dashboard/certificates",
    icon: Award,
    isActive: false,
  },
]

// Teacher routes for instructors
const teacherRoutes = [
  {
    title: "Kursus",
    url: "/teacher/courses",
    icon: List,
    isActive: false,
  },
  {
    title: "Kategori",
    url: "/teacher/categories",
    icon: Tags,
    isActive: false,
  },
  {
    title: "Analitik",
    url: "/teacher/analytics",
    icon: BarChart,
    isActive: false,
  },
  {
    title: "Review",
    url: "/teacher/reviews",
    icon: MessageSquare,
    isActive: false,
  },
  {
    title: "Sertifikat",
    url: "/teacher/certificates",
    icon: Award,
    isActive: false,
  },
]

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { userId } = useAuth()
  const pathname = usePathname()

  // Determine if user is a teacher
  const isTeacherUser = isTeacher(userId)
  const isTeacherPage = pathname?.startsWith('/teacher')

  // Get appropriate routes based on user role and current page
  const routes = isTeacherPage ? teacherRoutes : guestRoutes

  // Mark active route
  const navItems = routes.map(route => ({
    ...route,
    isActive: (pathname === '/' && route.url === '/') ||
              pathname === route.url ||
              pathname?.startsWith(`${route.url}/`)
  }))

  return (
    <Sidebar variant="inset" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <Link href="/">
                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                  <GraduationCap className="size-4" />
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold text-sm">LMS Platform</span>
                  <span className="truncate text-xs text-sidebar-foreground/70">Learning Management</span>
                </div>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={navItems} />
        <NavSecondary
          items={[]}
          className="mt-auto"
          isTeacher={isTeacherUser}
          isTeacherPage={isTeacherPage}
        />
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  )
}
