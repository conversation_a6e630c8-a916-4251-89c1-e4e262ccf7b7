"use client"

import { useA<PERSON>, <PERSON>r<PERSON><PERSON><PERSON> } from "@clerk/nextjs"

import {
  SidebarMenu,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import { ThemeToggle } from "@/components/theme-toggle"

export function NavUser() {
  const { isSignedIn } = useAuth()

  if (!isSignedIn) {
    return null
  }

  return (
    <SidebarMenu>
      <SidebarMenuItem className="flex flex-row items-center gap-2 p-2">
        {/* Theme Toggle */}
        <ThemeToggle />

        {/* User Profile with <PERSON> */}
        <UserButton
          appearance={{
            elements: {
              avatarBox: "h-8 w-8",
              userButtonTrigger: "focus:shadow-none"
            }
          }}
          showName={false}
          afterSignOutUrl="/"
        />
      </SidebarMenuItem>
    </SidebarMenu>
  )
}
