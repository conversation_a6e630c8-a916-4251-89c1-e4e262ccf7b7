import * as React from "react"
import { type LucideIcon, LogOut, UserCheck } from "lucide-react"
import Link from "next/link"

import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"

export function NavSecondary({
  items,
  isTeacher,
  isTeacherPage,
  ...props
}: {
  items: {
    title: string
    url: string
    icon: LucideIcon
  }[]
  isTeacher?: boolean
  isTeacherPage?: boolean
} & React.ComponentPropsWithoutRef<typeof SidebarGroup>) {
  return (
    <SidebarGroup {...props}>
      <SidebarGroupContent>
        <SidebarMenu>
          {/* Teacher mode toggle */}
          {isTeacher && (
            <SidebarMenuItem>
              <SidebarMenuButton asChild size="sm">
                {isTeacherPage ? (
                  <Link href="/">
                    <LogOut />
                    <span>Mode Siswa</span>
                  </Link>
                ) : (
                  <Link href="/teacher/courses">
                    <UserCheck />
                    <span>Mode Pengajar</span>
                  </Link>
                )}
              </SidebarMenuButton>
            </SidebarMenuItem>
          )}

          {/* Regular secondary items */}
          {items.map((item) => (
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton asChild size="sm">
                <a href={item.url}>
                  <item.icon />
                  <span>{item.title}</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}
