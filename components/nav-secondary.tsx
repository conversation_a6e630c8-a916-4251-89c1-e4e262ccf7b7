import * as React from "react"
import { type LucideIcon, LogOut, User<PERSON>he<PERSON>, Setting<PERSON> } from "lucide-react"
import Link from "next/link"

import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import { ThemeToggle } from "@/components/theme-toggle"

export function NavSecondary({
  items,
  isTeacher,
  isTeacherPage,
  ...props
}: {
  items: {
    title: string
    url: string
    icon: LucideIcon
  }[]
  isTeacher?: boolean
  isTeacherPage?: boolean
} & React.ComponentPropsWithoutRef<typeof SidebarGroup>) {
  return (
    <SidebarGroup {...props}>
      <SidebarGroupContent>
        <SidebarMenu>
          {/* Teacher mode toggle */}
          {isTeacher && (
            <SidebarMenuItem>
              <SidebarMenuButton asChild size="sm" className="text-sm">
                {isTeacherPage ? (
                  <Link href="/">
                    <LogOut className="size-4" />
                    <span>Mode Siswa</span>
                  </Link>
                ) : (
                  <Link href="/teacher/courses">
                    <UserCheck className="size-4" />
                    <span>Mode Pengajar</span>
                  </Link>
                )}
              </SidebarMenuButton>
            </SidebarMenuItem>
          )}

          {/* Theme Toggle */}
          <SidebarMenuItem>
            <div className="flex items-center gap-2 px-2 py-1">
              <Settings className="size-4" />
              <span className="text-sm">Tema</span>
              <div className="ml-auto">
                <ThemeToggle />
              </div>
            </div>
          </SidebarMenuItem>

          {/* Regular secondary items */}
          {items.map((item) => (
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton asChild size="sm" className="text-sm">
                <a href={item.url}>
                  <item.icon className="size-4" />
                  <span>{item.title}</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}
